# 角斗领域 - 守望先锋Stadium构筑工具

![角斗领域](https://img.shields.io/badge/角斗领域-守望先锋Stadium构筑工具-orange?style=for-the-badge)
![Next.js](https://img.shields.io/badge/Next.js-15.2.4-black?style=for-the-badge&logo=next.js)
![React](https://img.shields.io/badge/React-19-blue?style=for-the-badge&logo=react)
![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?style=for-the-badge&logo=typescript)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4-38B2AC?style=for-the-badge&logo=tailwind-css)

## 🏟️ 项目简介

**角斗领域**是一个专为守望先锋Stadium模式设计的英雄构筑工具。在这个终极竞技场中，玩家可以策略性地选择装备和异能，打造属于自己的传奇英雄构建。

### ✨ 核心特性

- 🎯 **英雄构筑模拟器** - 完整的7回合构筑流程模拟
- ⚡ **异能系统** - 支持士兵：76和朱诺的所有异能选择
- 🛡️ **装备系统** - 武器、技能、生存三大类装备，涵盖普通、稀有、史诗三种稀有度
- 📊 **BD概览** - 实时查看当前构筑配置
- 💾 **导入导出** - 保存和分享你的构筑配置
- 🎨 **守望先锋主题** - 采用官方配色方案的精美界面

### 🎮 支持英雄

- **士兵：76 (Soldier: 76)** - 18+ 种独特异能
- **朱诺 (Juno)** - 18+ 种独特异能

### 📦 装备数据

- **80+ 装备道具** - 涵盖所有Stadium模式装备
- **200+ 独特异能** - 丰富的异能组合可能性
- **∞ 构筑组合** - 无限的策略搭配空间

## 🚀 快速开始

### 环境要求

- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/overwatch-stadium-arena.git
   cd overwatch-stadium-arena
   ```

2. **安装依赖**
   ```bash
   npm install --legacy-peer-deps
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **访问应用**

   打开浏览器访问 [http://localhost:3000](http://localhost:3000)

### 构建部署

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

## 🎯 使用指南

### 基础操作

1. **选择英雄** - 在主界面选择士兵：76或朱诺
2. **配置异能** - 在第1、3、5、7回合选择异能
3. **选择装备** - 每回合选择最多6件装备
4. **设置优先级** - 为装备设置必选、经济、可选优先级
5. **查看概览** - 在BD概览中查看完整构筑

### 高级功能

- **导出配置** - 将当前构筑保存为JSON文件
- **导入配置** - 加载之前保存的构筑配置
- **装备筛选** - 按类别和稀有度筛选装备
- **实时预览** - 实时查看构筑效果和统计

## 🛠️ 技术栈

### 前端框架
- **Next.js 15.2.4** - React全栈框架
- **React 19** - 用户界面库
- **TypeScript 5** - 类型安全的JavaScript

### 样式设计
- **Tailwind CSS 3.4** - 原子化CSS框架
- **Radix UI** - 无障碍组件库
- **Lucide React** - 现代图标库

### 开发工具
- **ESLint** - 代码质量检查
- **PostCSS** - CSS后处理器
- **Autoprefixer** - CSS兼容性处理

## 📁 项目结构

```
overwatch-stadium-arena/
├── app/                    # Next.js App Router
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 主页
│   └── simulator/         # 模拟器页面
├── components/            # React组件
│   ├── ui/               # 基础UI组件
│   ├── item-card.tsx     # 装备卡片
│   ├── item-list.tsx     # 装备列表
│   ├── bd-overview.tsx   # BD概览
│   └── ...
├── data/                 # 游戏数据
│   ├── hero-powers.csv   # 异能数据
│   └── items.csv         # 装备数据
├── utils/               # 工具函数
│   ├── data-loader.ts   # 数据加载器
│   └── import-export.ts # 导入导出功能
└── public/              # 静态资源
```

## 🎨 设计理念

### 色彩方案
- **主色调** - 守望先锋橙色 (#f97316) 和蓝色 (#3b82f6)
- **辅助色** - 黄色 (#eab308) 和青色 (#06b6d4)
- **稀有度** - 绿色(普通)、蓝色(稀有)、紫色(史诗)

### 用户体验
- **直观操作** - 点击选择，拖拽排序
- **实时反馈** - 即时显示选择结果
- **响应式设计** - 适配桌面和移动设备

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- **暴雪娱乐** - 守望先锋游戏及Stadium模式
- **Radix UI** - 优秀的无障碍组件库
- **Tailwind CSS** - 强大的CSS框架
- **Next.js** - 出色的React框架

## 📞 联系我们

- **项目主页** - [GitHub Repository](https://github.com/your-username/overwatch-stadium-arena)
- **问题反馈** - [Issues](https://github.com/your-username/overwatch-stadium-arena/issues)
- **功能建议** - [Discussions](https://github.com/your-username/overwatch-stadium-arena/discussions)

---

<div align="center">

**为荣耀而战！在角斗领域中锻造您的传奇！**

![角斗领域Logo](https://img.shields.io/badge/⚔️-角斗领域-orange?style=for-the-badge)

</div>