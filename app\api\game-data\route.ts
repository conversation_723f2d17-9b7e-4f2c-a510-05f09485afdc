import { NextResponse } from "next/server"
import { loadHeroPowers, loadItems } from "../../../utils/data-loader"

export async function GET() {
  try {
    // 加载数据
    const powers = loadHeroPowers()
    const items = loadItems()

    // 返回数据
    return NextResponse.json({
      powers,
      items,
    })
  } catch (error) {
    console.error("Error loading game data:", error)
    return NextResponse.json({ error: "Failed to load game data" }, { status: 500 })
  }
}
