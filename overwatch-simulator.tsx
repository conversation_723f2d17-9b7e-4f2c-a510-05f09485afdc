"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AlertCircle, Check, X, Download, Upload } from "lucide-react"
import type { HeroPower, Item } from "./utils/data-loader"
import ItemImage from "./components/item-image"
import PowerImage from "./components/power-image"
import ItemList from "./components/item-list"
import BDOverview from "./components/bd-overview"
import ImportExportDialog from "./components/import-export-dialog"
import type { BDConfig } from "./utils/import-export"

export default function OverwatchBDSimulator() {
  // State
  const [heroPowers, setHeroPowers] = useState<Record<string, HeroPower[]>>({})
  const [itemsData, setItemsData] = useState<Item[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // 导入/导出对话框状态
  const [dialogOpen, setDialogOpen] = useState(false)
  const [dialogMode, setDialogMode] = useState<"import" | "export">("export")

  // 修复前端组件中的数据加载逻辑
  useEffect(() => {
    const loadData = async () => {
      try {
        // 尝试从API获取数据
        try {
          const response = await fetch("/api/game-data")
          if (response.ok) {
            const data = await response.json()

            // 按英雄组织异能数据
            const powersByHero: Record<string, HeroPower[]> = {}
            data.powers.forEach((power: HeroPower) => {
              if (!powersByHero[power.hero]) {
                powersByHero[power.hero] = []
              }
              powersByHero[power.hero].push(power)
            })

            setHeroPowers(powersByHero)
            setItemsData(data.items)
            setIsLoading(false)
            return
          }
        } catch (error) {
          console.error("API fetch failed, using fallback data:", error)
        }

        // 如果API调用失败，直接从data-loader加载
        const { loadHeroPowers, loadItems } = await import("./utils/data-loader")
        const powers = loadHeroPowers()
        const items = loadItems()

        // 按英雄组织异能数据
        const powersByHero: Record<string, HeroPower[]> = {}
        powers.forEach((power: HeroPower) => {
          if (!powersByHero[power.hero]) {
            powersByHero[power.hero] = []
          }
          powersByHero[power.hero].push(power)
        })

        setHeroPowers(powersByHero)
        setItemsData(items)
      } catch (error) {
        console.error("Error loading data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [])

  // State
  const [currentHero, setCurrentHero] = useState("soldier76")
  const [selectedPowerSlots, setSelectedPowerSlots] = useState([null, null, null, null])
  const [currentRound, setCurrentRound] = useState(1)
  const [currentItemCategory, setCurrentItemCategory] = useState("weapon")
  const [roundItemSelections, setRoundItemSelections] = useState(
    Array(7)
      .fill(null)
      .map(() => Array(6).fill(null)),
  )
  const [notification, setNotification] = useState({ message: "", type: "", visible: false })

  // 获取当前配置
  const getCurrentConfig = (): BDConfig => {
    return {
      hero: currentHero,
      powers: selectedPowerSlots,
      items: roundItemSelections,
      version: "1.0", // 版本号，用于未来兼容性
    }
  }

  // 导入配置
  const handleImportConfig = (config: BDConfig) => {
    try {
      setCurrentHero(config.hero)
      setSelectedPowerSlots(config.powers)
      setRoundItemSelections(config.items)
      setCurrentRound(1)
      showNotification("配置导入成功！", "info")
    } catch (error) {
      showNotification("配置导入失败，请检查格式", "error")
    }
  }

  // 打开导出对话框
  const openExportDialog = () => {
    setDialogMode("export")
    setDialogOpen(true)
  }

  // 打开导入对话框
  const openImportDialog = () => {
    setDialogMode("import")
    setDialogOpen(true)
  }

  // Helper functions
  const getHeroName = (heroId) => {
    return heroId === "soldier76" ? "士兵：76 (Soldier: 76)" : "朱诺 (Juno)"
  }

  if (isLoading) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Overwatch-style Geometric Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-300 via-blue-200 to-purple-200">
          {/* Large geometric shapes */}
          <div className="absolute inset-0">
            {/* Main diagonal shapes */}
            <div className="absolute top-0 left-0 w-full h-full">
              <svg className="w-full h-full" viewBox="0 0 1920 1080" preserveAspectRatio="xMidYMid slice">
                <defs>
                  <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style={{stopColor: '#e2e8f0', stopOpacity: 0.8}} />
                    <stop offset="50%" style={{stopColor: '#cbd5e1', stopOpacity: 0.6}} />
                    <stop offset="100%" style={{stopColor: '#94a3b8', stopOpacity: 0.4}} />
                  </linearGradient>
                  <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style={{stopColor: '#dbeafe', stopOpacity: 0.7}} />
                    <stop offset="100%" style={{stopColor: '#bfdbfe', stopOpacity: 0.5}} />
                  </linearGradient>
                  <linearGradient id="grad3" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style={{stopColor: '#e0e7ff', stopOpacity: 0.6}} />
                    <stop offset="100%" style={{stopColor: '#c7d2fe', stopOpacity: 0.4}} />
                  </linearGradient>
                </defs>

                {/* Large diagonal triangles */}
                <polygon points="0,0 800,0 400,600" fill="url(#grad1)" />
                <polygon points="1920,0 1920,400 1200,800" fill="url(#grad2)" />
                <polygon points="0,1080 600,1080 300,500" fill="url(#grad3)" />
                <polygon points="1920,1080 1920,600 1400,1080" fill="url(#grad1)" />

                {/* Medium triangles */}
                <polygon points="600,200 1000,200 800,600" fill="url(#grad2)" opacity="0.6" />
                <polygon points="1200,400 1600,400 1400,800" fill="url(#grad3)" opacity="0.5" />
                <polygon points="200,700 600,700 400,1080" fill="url(#grad1)" opacity="0.4" />

                {/* Small accent triangles */}
                <polygon points="300,100 500,100 400,300" fill="#f97316" opacity="0.3" />
                <polygon points="1500,200 1700,200 1600,400" fill="#3b82f6" opacity="0.3" />
                <polygon points="100,800 300,800 200,1000" fill="#eab308" opacity="0.2" />
                <polygon points="1600,700 1800,700 1700,900" fill="#06b6d4" opacity="0.2" />
              </svg>
            </div>

            {/* Floating particles/dots */}
            <div className="absolute inset-0">
              <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-orange-400 rounded-full opacity-60 animate-pulse"></div>
              <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-blue-500 rounded-full opacity-50 animate-pulse" style={{animationDelay: '1s'}}></div>
              <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-yellow-400 rounded-full opacity-40 animate-pulse" style={{animationDelay: '2s'}}></div>
              <div className="absolute top-2/3 right-1/4 w-1 h-1 bg-cyan-400 rounded-full opacity-50 animate-pulse" style={{animationDelay: '0.5s'}}></div>
              <div className="absolute bottom-1/3 right-2/3 w-2 h-2 bg-purple-400 rounded-full opacity-30 animate-pulse" style={{animationDelay: '1.5s'}}></div>
            </div>
          </div>
        </div>

        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-orange-600 via-orange-500 to-blue-600 bg-clip-text text-transparent">角斗领域启动中...</h2>
            <p className="text-gray-700">请稍候，正在准备Stadium构筑工坊</p>
          </div>
        </div>
      </div>
    )
  }

  const getItemCategoryText = (category) => {
    switch (category) {
      case "weapon":
        return "武器"
      case "ability":
        return "技能"
      case "survival":
        return "生存"
      default:
        return "未知"
    }
  }

  const getPriorityText = (priority) => {
    switch (priority) {
      case "must":
        return "必选项 (红)"
      case "affordable":
        return "有钱必买 (黄)"
      case "optional":
        return "可选项 (绿)"
      default:
        return "无"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "must":
        return "border-red-500"
      case "affordable":
        return "border-yellow-500"
      case "optional":
        return "border-green-500"
      default:
        return ""
    }
  }

  const getRarityColor = (rarity) => {
    switch (rarity) {
      case "common":
        return "text-white"
      case "rare":
        return "text-blue-400"
      case "epic":
        return "text-purple-400"
      default:
        return "text-white"
    }
  }

  const showNotification = (message, type = "info") => {
    setNotification({ message, type, visible: true })
    setTimeout(() => {
      setNotification((prev) => ({ ...prev, visible: false }))
    }, 3000)
  }

  // Event handlers
  const handleHeroChange = (value) => {
    setCurrentHero(value)
    setSelectedPowerSlots([null, null, null, null])
    setRoundItemSelections(
      Array(7)
        .fill(null)
        .map(() => Array(6).fill(null)),
    )
    setCurrentRound(1)
    showNotification(`已切换到 ${value === "soldier76" ? "士兵：76" : "朱诺"}，异能和物品配置已重置。`)
  }

  const handlePowerSelection = (powerId) => {
    const emptySlotIndex = selectedPowerSlots.findIndex((slot) => slot === null)
    if (emptySlotIndex === -1) {
      showNotification("所有异能插槽已满！", "error")
      return
    }

    const newSelectedPowers = [...selectedPowerSlots]
    newSelectedPowers[emptySlotIndex] = powerId
    setSelectedPowerSlots(newSelectedPowers)
  }

  const handleClearPowerSlot = (slotIndex) => {
    const newSelectedPowers = [...selectedPowerSlots]
    newSelectedPowers[slotIndex] = null
    setSelectedPowerSlots(newSelectedPowers)
  }

  const resetPowerSelections = () => {
    setSelectedPowerSlots([null, null, null, null])
  }

  const handleItemSelection = (itemId) => {
    const currentRoundSelection = [...roundItemSelections[currentRound - 1]]

    if (currentRoundSelection.some((slot) => slot && slot.itemId === itemId)) {
      showNotification("本回合已选择该物品！", "error")
      return
    }

    const emptySlotIndex = currentRoundSelection.findIndex((slot) => slot === null)
    if (emptySlotIndex === -1) {
      showNotification(`第 ${currentRound} 回合物品栏已满！`, "error")
      return
    }

    currentRoundSelection[emptySlotIndex] = { itemId, priority: "none" }

    const newRoundItemSelections = [...roundItemSelections]
    newRoundItemSelections[currentRound - 1] = currentRoundSelection
    setRoundItemSelections(newRoundItemSelections)
  }

  const handleClearItemSlot = (slotIndex) => {
    const currentRoundSelection = [...roundItemSelections[currentRound - 1]]
    currentRoundSelection[slotIndex] = null

    const newRoundItemSelections = [...roundItemSelections]
    newRoundItemSelections[currentRound - 1] = currentRoundSelection
    setRoundItemSelections(newRoundItemSelections)
  }

  const handlePriorityToggle = (slotIndex) => {
    const currentRoundSelection = [...roundItemSelections[currentRound - 1]]
    const slotData = currentRoundSelection[slotIndex]

    if (slotData) {
      const priorities = ["none", "must", "affordable", "optional"]
      const currentPriorityIndex = priorities.indexOf(slotData.priority)
      const nextPriorityIndex = (currentPriorityIndex + 1) % priorities.length
      slotData.priority = priorities[nextPriorityIndex]

      const newRoundItemSelections = [...roundItemSelections]
      newRoundItemSelections[currentRound - 1] = currentRoundSelection
      setRoundItemSelections(newRoundItemSelections)
    }
  }

  const resetCurrentRoundItems = () => {
    const newRoundItemSelections = [...roundItemSelections]
    newRoundItemSelections[currentRound - 1] = Array(6).fill(null)
    setRoundItemSelections(newRoundItemSelections)
  }

  const completeCurrentRound = () => {
    if (currentRound < 7) {
      setCurrentRound(currentRound + 1)
    } else {
      showNotification("已完成所有回合配置！")
    }
  }

  // Update the filtering logic to properly handle hero-specific items
  const heroFilteredItems = itemsData.filter(
    (item) => item.category === currentItemCategory && (item.hero === "general" || item.hero === currentHero),
  )

  // Group items by rarity
  const commonItems = heroFilteredItems.filter((item) => item.rarity === "common")
  const rareItems = heroFilteredItems.filter((item) => item.rarity === "rare")
  const epicItems = heroFilteredItems.filter((item) => item.rarity === "epic")

  // Check if any items are selected for overview
  const hasAnyItems = roundItemSelections.some((round) => round.some((slot) => slot !== null))

  // Render a single item slot in the current round
  const renderItemSlot = (slotIndex) => {
    const slotData = roundItemSelections[currentRound - 1][slotIndex]
    const item = slotData ? itemsData.find((item) => item.id === slotData.itemId) : null

    return (
      <div
        key={`item-slot-${slotIndex}`}
        onClick={() => item && handlePriorityToggle(slotIndex)}
        className={`relative p-3 rounded-lg flex flex-col items-center justify-center min-h-[100px] transition-all ${
          item
            ? `bg-white border-2 ${getPriorityColor(slotData.priority) || "border-gray-300"} cursor-pointer`
            : "bg-gray-100 border-2 border-dashed border-gray-400"
        }`}
      >
        {item ? (
          <>
            {item.hero !== "general" && (
              <div
                className={`absolute top-0 right-0 text-xs font-bold px-1 rounded-bl text-white ${
                  item.rarity === "rare" ? "bg-blue-600" : "bg-purple-600"
                }`}
              >
                Hero
              </div>
            )}
            <ItemImage itemId={item.id} name={item.name} className="rounded-md mb-1" />
            <div className="text-xs font-semibold text-center text-gray-800">{item.name}</div>
            <div className="text-xs text-gray-600 text-center">({getItemCategoryText(item.category)})</div>

            <button
              onClick={(e) => {
                e.stopPropagation()
                handleClearItemSlot(slotIndex)
              }}
              className="absolute top-1 right-1 w-5 h-5 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center opacity-0 hover:opacity-100"
            >
              <X className="w-3 h-3 text-white" />
            </button>

            <div className="absolute inset-0 opacity-0 hover:opacity-100 bg-gray-900/80 rounded-lg transition-opacity flex items-center justify-center z-20">
              <div className="bg-white p-3 rounded-md max-w-xs border border-gray-300">
                <div className="font-bold text-orange-600">
                  {item.name} ({item.eng_name})
                </div>
                <div className="text-xs text-orange-500 mb-1">价格: {item.cost} C</div>
                <div className="text-sm mt-1 text-gray-700">
                  {item.description && item.description.split("\n").map((line, i) => <div key={i}>{line}</div>)}
                </div>
                <div className="text-xs text-gray-600 mt-2">
                  优先级: {getPriorityText(slotData.priority)} (点击切换)
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="text-gray-500 text-xs">空槽位</div>
        )}
      </div>
    )
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Overwatch-style Geometric Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-300 via-blue-200 to-purple-200">
        {/* Large geometric shapes */}
        <div className="absolute inset-0">
          {/* Main diagonal shapes */}
          <div className="absolute top-0 left-0 w-full h-full">
            <svg className="w-full h-full" viewBox="0 0 1920 1080" preserveAspectRatio="xMidYMid slice">
              <defs>
                <linearGradient id="mainGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style={{stopColor: '#e2e8f0', stopOpacity: 0.8}} />
                  <stop offset="50%" style={{stopColor: '#cbd5e1', stopOpacity: 0.6}} />
                  <stop offset="100%" style={{stopColor: '#94a3b8', stopOpacity: 0.4}} />
                </linearGradient>
                <linearGradient id="mainGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style={{stopColor: '#dbeafe', stopOpacity: 0.7}} />
                  <stop offset="100%" style={{stopColor: '#bfdbfe', stopOpacity: 0.5}} />
                </linearGradient>
                <linearGradient id="mainGrad3" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style={{stopColor: '#e0e7ff', stopOpacity: 0.6}} />
                  <stop offset="100%" style={{stopColor: '#c7d2fe', stopOpacity: 0.4}} />
                </linearGradient>
              </defs>

              {/* Large diagonal triangles */}
              <polygon points="0,0 800,0 400,600" fill="url(#mainGrad1)" />
              <polygon points="1920,0 1920,400 1200,800" fill="url(#mainGrad2)" />
              <polygon points="0,1080 600,1080 300,500" fill="url(#mainGrad3)" />
              <polygon points="1920,1080 1920,600 1400,1080" fill="url(#mainGrad1)" />

              {/* Medium triangles */}
              <polygon points="600,200 1000,200 800,600" fill="url(#mainGrad2)" opacity="0.6" />
              <polygon points="1200,400 1600,400 1400,800" fill="url(#mainGrad3)" opacity="0.5" />
              <polygon points="200,700 600,700 400,1080" fill="url(#mainGrad1)" opacity="0.4" />

              {/* Small accent triangles */}
              <polygon points="300,100 500,100 400,300" fill="#f97316" opacity="0.3" />
              <polygon points="1500,200 1700,200 1600,400" fill="#3b82f6" opacity="0.3" />
              <polygon points="100,800 300,800 200,1000" fill="#eab308" opacity="0.2" />
              <polygon points="1600,700 1800,700 1700,900" fill="#06b6d4" opacity="0.2" />
            </svg>
          </div>

          {/* Floating particles/dots */}
          <div className="absolute inset-0">
            <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-orange-400 rounded-full opacity-60 animate-pulse"></div>
            <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-blue-500 rounded-full opacity-50 animate-pulse" style={{animationDelay: '1s'}}></div>
            <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-yellow-400 rounded-full opacity-40 animate-pulse" style={{animationDelay: '2s'}}></div>
            <div className="absolute top-2/3 right-1/4 w-1 h-1 bg-cyan-400 rounded-full opacity-50 animate-pulse" style={{animationDelay: '0.5s'}}></div>
            <div className="absolute bottom-1/3 right-2/3 w-2 h-2 bg-purple-400 rounded-full opacity-30 animate-pulse" style={{animationDelay: '1.5s'}}></div>
          </div>
        </div>
      </div>

      {/* Notification */}
      {notification.visible && (
        <div
          className={`fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg transition-all transform ${
            notification.type === "error" ? "bg-red-600" : "bg-blue-600"
          }`}
        >
          <div className="flex items-center">
            {notification.type === "error" ? (
              <AlertCircle className="mr-2 h-5 w-5 text-white" />
            ) : (
              <Check className="mr-2 h-5 w-5 text-white" />
            )}
            <p className="text-white">{notification.message}</p>
          </div>
        </div>
      )}

      {/* Import/Export Dialog */}
      <ImportExportDialog
        isOpen={dialogOpen}
        onClose={() => setDialogOpen(false)}
        currentConfig={getCurrentConfig()}
        onImport={handleImportConfig}
        mode={dialogMode}
      />

      <div className="relative z-10 max-w-7xl mx-auto p-4 md:p-8">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="text-right">
              <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-orange-600 via-orange-500 to-blue-600 bg-clip-text text-transparent mb-2">
                角斗领域构筑工坊
              </h1>
              <p className="text-xl text-gray-700 font-semibold tracking-wider">
                OVERWATCH STADIUM BUILD
              </p>
            </div>
          </div>
        </div>

        {/* Hero Selection and Import/Export Buttons */}
        <Card className="bg-white/70 border-2 border-orange-300/60 backdrop-blur-sm rounded-lg shadow-xl mb-8 p-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="text-center md:text-left flex-1">
              <h2 className="text-xl font-bold mb-4 text-gray-800">选择英雄</h2>
              <Select value={currentHero} onValueChange={handleHeroChange}>
                <SelectTrigger className="w-full max-w-xs mx-auto md:mx-0 bg-white border-gray-300 text-gray-800">
                  <SelectValue placeholder="选择英雄" />
                </SelectTrigger>
                <SelectContent className="bg-white border-gray-300 text-gray-800">
                  <SelectItem value="soldier76">士兵：76 (Soldier: 76)</SelectItem>
                  <SelectItem value="juno">朱诺 (Juno)</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-gray-600 mt-2">
                （当前选择：{currentHero === "soldier76" ? "士兵：76" : "朱诺"}）
              </p>
            </div>

            <div className="flex justify-center md:justify-end">
              <Button onClick={openImportDialog} className="bg-blue-600 hover:bg-blue-700 text-white">
                <Upload className="mr-2 h-4 w-4" />
                导入配置
              </Button>
            </div>
          </div>
        </Card>

        {/* Powers Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Selected Powers */}
          <Card className="bg-white/70 border-2 border-blue-300/60 backdrop-blur-sm rounded-lg shadow-xl p-6">
            <h2 className="text-xl font-bold mb-4 text-center text-blue-600">已选异能 (Powers)</h2>
            <div className="space-y-3">
              {[1, 3, 5, 7].map((round, index) => {
                const powerId = selectedPowerSlots[index]
                const power = powerId ? heroPowers[currentHero]?.find((p) => p.id === powerId) : null

                return (
                  <div
                    key={`power-slot-${index}`}
                    className={`relative flex items-center p-4 rounded-lg transition-all ${
                      power
                        ? "bg-white border-2 border-blue-400"
                        : "bg-white border-2 border-dashed border-gray-400"
                    }`}
                  >
                    {power ? (
                      <>
                        <div className="flex items-center gap-3 flex-1 min-h-[60px]">
                          <PowerImage powerId={power.id} name={power.name} className="rounded-md" />
                          <div>
                            <div className="font-semibold text-gray-800">{power.name}</div>
                            <div className="text-xs text-gray-600">第 {round} 回合</div>
                          </div>
                        </div>
                        <button
                          onClick={() => handleClearPowerSlot(index)}
                          className="w-6 h-6 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center relative z-20"
                        >
                          <X className="w-4 h-4 text-white" />
                        </button>
                      </>
                    ) : (
                      <div className="flex items-center min-h-[60px] text-gray-500 w-full text-center">
                        第 {round} 回合 - 点击右侧选择
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
            <div className="mt-4 text-center">
              <Button onClick={resetPowerSelections} className="bg-red-600 hover:bg-red-700 text-white">
                重置异能
              </Button>
            </div>
          </Card>

          {/* Available Powers */}
          <Card className="lg:col-span-2 bg-white/70 border-2 border-green-300/60 backdrop-blur-sm rounded-lg shadow-xl p-6">
            <h2 className="text-xl font-bold mb-4 text-center text-green-600">
              {currentHero === "soldier76" ? "士兵：76" : "朱诺"} 可选异能
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {heroPowers[currentHero]?.map((power) => {
                const isSelected = selectedPowerSlots.includes(power.id)

                return (
                  <div
                    key={power.id}
                    onClick={() => !isSelected && handlePowerSelection(power.id)}
                    className={`relative p-3 rounded-lg flex flex-col items-center justify-center transition-all ${
                      isSelected
                        ? "bg-white border-2 border-green-500 cursor-not-allowed"
                        : `bg-white border border-gray-300 hover:border-${power.id.includes("s76") ? "blue" : "green"}-400 hover:bg-gray-50 cursor-pointer`
                    }`}
                  >
                    <PowerImage powerId={power.id} name={power.name} className={`rounded-md mb-2 ${isSelected ? "opacity-80" : ""}`} size={48} />
                    <div className={`font-semibold text-center ${isSelected ? "text-gray-700" : "text-gray-800"}`}>{power.name}</div>
                    <div className={`text-xs text-center ${isSelected ? "text-gray-500" : "text-gray-600"}`}>{power.eng_name}</div>

                    {isSelected && (
                      <div className="absolute inset-0 bg-green-100/60 rounded-lg flex items-center justify-center">
                        <div className="bg-white/95 px-3 py-1 rounded-md border border-green-400 shadow-sm">
                          <div className="text-sm font-semibold text-green-600">已选择</div>
                        </div>
                      </div>
                    )}

                    {!isSelected && (
                      <div className="absolute inset-0 opacity-0 hover:opacity-100 bg-white/95 rounded-lg transition-opacity flex items-center justify-center z-10">
                        <div className="bg-white p-3 rounded-md max-w-xs border border-gray-300 shadow-lg">
                          <div className={`font-bold ${power.id.includes("s76") ? "text-blue-600" : "text-green-600"}`}>
                            {power.name}
                          </div>
                          <div
                            className="text-sm mt-1 text-gray-700"
                            dangerouslySetInnerHTML={{
                              __html: power.description
                                ? power.description.replace(/\[(.*?)\]/g, '<span class="text-orange-600 font-semibold">[$1]</span>')
                                : "无描述",
                            }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          </Card>
        </div>

        {/* Items Section */}
        <Card className="bg-white/70 border-2 border-yellow-300/60 backdrop-blur-sm rounded-lg shadow-xl mb-8 p-6">
          <h2 className="text-2xl font-bold mb-5 text-center text-yellow-600">物品配置 (Items)</h2>

          {/* Round Navigation */}
          <div className="flex justify-center flex-wrap mb-6 gap-2">
            {Array.from({ length: 7 }, (_, i) => i + 1).map((round) => (
              <Button
                key={`round-${round}`}
                onClick={() => setCurrentRound(round)}
                className={`${
                  currentRound === round
                    ? "bg-yellow-600 text-white"
                    : "bg-gray-200 text-gray-700 hover:bg-yellow-200"
                }`}
              >
                第 {round} 回合
              </Button>
            ))}
          </div>

          {/* Current Round Items */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-center text-gray-700">
              第 {currentRound} 回合 物品栏 (最多 6 件)
            </h3>
            <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
              {Array.from({ length: 6 }, (_, i) => renderItemSlot(i))}
            </div>
          </div>

          {/* Round Controls */}
          <div className="flex justify-center space-x-4 mb-6">
            <Button onClick={resetCurrentRoundItems} className="bg-red-600 hover:bg-red-700 text-white">
              重置本回合物品
            </Button>
            <Button onClick={completeCurrentRound} className="bg-blue-600 hover:bg-blue-700 text-white">
              完成本回合配置
            </Button>
          </div>

          {/* Item Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-3 text-center text-gray-700">可选物品列表</h3>
            <p className="text-xs text-center text-gray-600 mb-3">(点击物品添加到上方空槽，点击槽内物品切换优先级)</p>

            <div className="flex border-b border-gray-300 mb-4">
              {["weapon", "ability", "survival"].map((category) => (
                <button
                  key={category}
                  onClick={() => setCurrentItemCategory(category)}
                  className={`px-4 py-2 font-medium transition-colors ${
                    currentItemCategory === category
                      ? "text-yellow-600 border-b-2 border-yellow-600 bg-yellow-50"
                      : "text-gray-600 hover:text-gray-800 hover:bg-gray-100"
                  }`}
                >
                  {getItemCategoryText(category)}
                </button>
              ))}
            </div>

            {/* Items by Rarity - Refactored to use ItemList component */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <ItemList
                title="普通"
                items={commonItems}
                rarity="common"
                selectedItems={roundItemSelections[currentRound - 1]}
                onItemSelect={handleItemSelection}
              />

              <ItemList
                title="稀有"
                items={rareItems}
                rarity="rare"
                selectedItems={roundItemSelections[currentRound - 1]}
                onItemSelect={handleItemSelection}
              />

              <ItemList
                title="史诗"
                items={epicItems}
                rarity="epic"
                selectedItems={roundItemSelections[currentRound - 1]}
                onItemSelect={handleItemSelection}
              />
            </div>
          </div>
        </Card>

        {/* Build Overview - Using the new compact component */}
        <BDOverview
          currentHero={currentHero}
          selectedPowerSlots={selectedPowerSlots}
          roundItemSelections={roundItemSelections}
          heroPowers={heroPowers}
          itemsData={itemsData}
          onExport={openExportDialog}
        />
      </div>
    </div>
  )
}
