"use client"

import { useState } from "react"
import { getItemImagePath } from "../utils/data-loader"

interface ItemImageProps {
  itemId: string
  name: string
  className?: string
  size?: number
  fallbackText?: string
}

export default function ItemImage({ itemId, name, className = "", size = 40, fallbackText }: ItemImageProps) {
  const [imageError, setImageError] = useState(false)

  // 直接使用getItemImagePath函数获取图片URL
  const imageSrc = getItemImagePath(itemId)

  // 处理图片加载错误
  const handleImageError = () => {
    console.log(`图片加载失败: ${imageSrc}`)
    setImageError(true)
  }

  // 如果图片加载失败，显示首字母
  if (imageError) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-700 ${className}`}
        style={{ width: size, height: size }}
      >
        <span className="text-lg font-bold">{fallbackText || name.charAt(0)}</span>
      </div>
    )
  }

  return (
    <div className={`overflow-hidden ${className}`} style={{ width: size, height: size }}>
      <img
        src={imageSrc || "/placeholder.svg"}
        alt={name}
        className="w-full h-full object-cover"
        onError={handleImageError}
        width={size}
        height={size}
        crossOrigin="anonymous"
      />
    </div>
  )
}
