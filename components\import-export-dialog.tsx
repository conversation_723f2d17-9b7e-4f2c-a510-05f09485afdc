"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Copy, Check, Import, AlertCircle } from "lucide-react"
import { type BDConfig, exportConfig, importConfig } from "../utils/import-export"

interface ImportExportDialogProps {
  isOpen: boolean
  onClose: () => void
  currentConfig: BDConfig
  onImport: (config: BDConfig) => void
  mode: "import" | "export"
}

export default function ImportExportDialog({
  isOpen,
  onClose,
  currentConfig,
  onImport,
  mode,
}: ImportExportDialogProps) {
  const [configString, setConfigString] = useState("")
  const [copied, setCopied] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [importedConfig, setImportedConfig] = useState<BDConfig | null>(null)

  // 处理导出
  const handleExport = () => {
    try {
      const exportedString = exportConfig(currentConfig)
      setConfigString(exportedString)
      setError(null)
    } catch (err) {
      setError("导出失败，请稍后重试")
    }
  }

  // 处理导入
  const handleImport = () => {
    try {
      if (!configString.trim()) {
        setError("请输入配置代码")
        return
      }

      const config = importConfig(configString)

      // 验证导入的配置
      if (!config.hero || !Array.isArray(config.powers) || !Array.isArray(config.items)) {
        setError("配置格式无效")
        return
      }

      // 设置导入的配置
      setImportedConfig(config)
      onImport(config)
      onClose()
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : "导入失败，请检查代码格式")
    }
  }

  // 复制到剪贴板
  const copyToClipboard = () => {
    navigator.clipboard.writeText(configString)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  // 当对话框打开时，如果是导出模式，自动生成配置字符串
  useEffect(() => {
    if (isOpen) {
      if (mode === "export") {
        handleExport()
      } else {
        setConfigString("")
        setError(null)
      }
    }
  }, [isOpen, mode, currentConfig])

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md bg-gray-800 text-white border-gray-700">
        <DialogHeader>
          <DialogTitle>{mode === "export" ? "导出配置" : "导入配置"}</DialogTitle>
          <DialogDescription className="text-gray-400">
            {mode === "export" ? "复制下面的代码以保存或分享你的BD配置" : "粘贴配置代码以导入BD配置"}
          </DialogDescription>
        </DialogHeader>

        {error && (
          <div className="bg-red-900/30 border border-red-700 rounded-md p-3 flex items-start gap-2">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
            <p className="text-sm text-red-300">{error}</p>
          </div>
        )}

        {mode === "export" ? (
          <>
            <div className="flex flex-col gap-2">
              <label htmlFor="config-string" className="text-sm font-medium">
                配置代码
              </label>
              <div className="relative">
                <Textarea
                  id="config-string"
                  value={configString}
                  readOnly
                  className="h-32 bg-gray-900 border-gray-700 text-white"
                />
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={copyToClipboard}
                  className="absolute top-2 right-2 h-8 px-2 text-gray-400 hover:text-white hover:bg-gray-700"
                >
                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
              <p className="text-xs text-gray-400">保存此代码以便将来导入，或分享给其他玩家</p>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={onClose} className="border-gray-600 text-gray-300 hover:bg-gray-700">
                关闭
              </Button>
            </div>
          </>
        ) : (
          <>
            <div className="flex flex-col gap-2">
              <label htmlFor="import-string" className="text-sm font-medium">
                粘贴配置代码
              </label>
              <Textarea
                id="import-string"
                value={configString}
                onChange={(e) => setConfigString(e.target.value)}
                placeholder="在此粘贴配置代码..."
                className="h-32 bg-gray-900 border-gray-700 text-white"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={onClose} className="border-gray-600 text-gray-300 hover:bg-gray-700">
                取消
              </Button>
              <Button onClick={handleImport} className="bg-blue-600 hover:bg-blue-700">
                <Import className="h-4 w-4 mr-2" />
                导入
              </Button>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}
