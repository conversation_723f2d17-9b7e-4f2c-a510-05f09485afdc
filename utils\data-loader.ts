// 定义数据类型
export interface HeroPower {
  id: string
  hero: string
  name: string
  eng_name: string
  description: string
}

export interface Item {
  id: string
  name: string
  eng_name: string
  description: string
  cost: number
  category: "weapon" | "ability" | "survival"
  rarity: "common" | "rare" | "epic"
  hero: string
}

// 获取物品图片路径
export function getItemImagePath(itemId: string): string {
  // 使用图床链接
  return `https://photo.drhorry.com/file/items/${itemId}.png`
}

// 修改getPowerImagePath函数，为士兵76添加特殊处理

export function getPowerImagePath(powerId: string): string {
  // 确保powerId是正确的格式，移除可能的前缀
  const cleanPowerId = powerId.replace(/^power_/, "")

  // 检查是否为士兵76的异能
  if (cleanPowerId.startsWith("s76_")) {
    // 为士兵76返回一个占位符图片URL
    return `/placeholder.svg?height=40&width=40&query=soldier76 ${cleanPowerId} ability icon`
  }

  // 朱诺的异能使用正常的图片路径
  return `https://photo.drhorry.com/file/power/${cleanPowerId}.png`
}

// 修改导入部分，移除对不存在的JSON文件的引用
// 从CSV数据转换而来的JSON数据
// 这些数据会在构建时从CSV转换为JSON
// import heroPowersData from "../data/hero-powers.json"
// import itemsData from "../data/items.json"

// 替换为基本的测试数据
// 基本测试数据
const TEST_HERO_POWERS: HeroPower[] = [
  {
    id: "s76_p1",
    hero: "soldier76",
    name: "附带脉冲",
    eng_name: "Peripheral Pulse",
    description: "在[战术目镜]期间，[重型脉冲步枪]可向额外一名敌人射击，对其造成50%的伤害。",
  },
  {
    id: "s76_p2",
    hero: "soldier76",
    name: "超级目镜",
    eng_name: "Super Visor",
    description: "使用[螺旋飞弹]之后，[战术目镜]将启动0.75秒。",
  },
  {
    id: "s76_p3",
    hero: "soldier76",
    name: "一路狂奔",
    eng_name: "Man on the Run",
    description: "在[疾跑]期间，每秒增加10%的弹药与弹匣容量，最多增加100%，装填时重置。",
  },
  {
    id: "s76_p4",
    hero: "soldier76",
    name: "链式步枪",
    eng_name: "Chaingun",
    description: "[重型脉冲步枪]不间断射击时，每次射击获得0.5%的武器强度，可叠加100次。",
  },
  {
    id: "s76_p5",
    hero: "soldier76",
    name: "战术整备",
    eng_name: "Hunker Down",
    description: "[螺旋飞弹]造成伤害时，在你脚下生成一个持续时间缩短70%的[生物力场]。",
  },
  {
    id: "s76_p6",
    hero: "soldier76",
    name: "双螺旋",
    eng_name: "Double Helix",
    description: "[螺旋飞弹]射出额外一发自动追踪的飞弹，造成30%的伤害。",
  },
  {
    id: "s76_p7",
    hero: "soldier76",
    name: "增爆飞弹",
    eng_name: "Cratered",
    description: "[螺旋飞弹]的溅射范围增加40%，溅射伤害增加25%。",
  },
  {
    id: "s76_p8",
    hero: "soldier76",
    name: "生物靶心",
    eng_name: "Biotic Bullseye",
    description: "在[生物力场]中造成暴击时，恢复5%弹药，并使[生物力场]的持续时间延长0.5秒，最多延长5秒。",
  },
  {
    id: "s76_p9",
    hero: "soldier76",
    name: "前线将士",
    eng_name: "Frontliners",
    description: "[生物力场]中的盟友获得你最大生命值30%的过量生命值，持续3秒。",
  },
  {
    id: "s76_p10",
    hero: "soldier76",
    name: "离我远点",
    eng_name: "Back Off",
    description: "[生物力场]对敌人造成与治疗量相当的伤害。",
  },
  {
    id: "s76_p11",
    hero: "soldier76",
    name: "向我靠拢！",
    eng_name: "On Me!",
    description: "[生物力场]跟随你移动，在持续时间内为你提供20%最大生命值。",
  },
  {
    id: "s76_p12",
    hero: "soldier76",
    name: "疾奔力场",
    eng_name: "Track and Field",
    description: "在[疾跑]期间，[生物力场]的冷却速度增加150%。",
  },
  {
    id: "juno_p1",
    hero: "juno",
    name: "冲击蜇刺",
    eng_name: "Stinger",
    description: "[医疗冲击枪]命中敌人时在1秒内造成10点伤害，不可叠加",
  },
  {
    id: "juno_p2",
    hero: "juno",
    name: "医疗精锐",
    eng_name: "Medimaster",
    description: "[医疗冲击枪]对盟友和敌人都可以造成暴击",
  },
  {
    id: "juno_p3",
    hero: "juno",
    name: "医疗飞弹",
    eng_name: "Medicinal Missiles",
    description: "[脉冲星飞雷]额外治疗30点生命值，并使被命中的盟友受到的治疗量提高50%，持续3秒",
  },
  {
    id: "juno_p4",
    hero: "juno",
    name: "多发脉冲星",
    eng_name: "Pulsar Plus",
    description: "[脉冲星飞雷]获得1次额外充能",
  },
  {
    id: "juno_p5",
    hero: "juno",
    name: "宇宙冷却剂",
    eng_name: "Cosmic Coolant",
    description: "[脉冲星飞雷]每命中一个目标，冷却时间缩短0.5秒",
  },
  {
    id: "juno_p6",
    hero: "juno",
    name: "闪现推进",
    eng_name: "Blink Boosts",
    description: "[滑翔推进]获得2次额外充能，冷却时间减少65%，但持续时间同时缩短75%",
  },
  {
    id: "juno_p7",
    hero: "juno",
    name: "飞雷滑翔",
    eng_name: "Torpedo Glide",
    description: "[滑翔推进]持续期间，每造成50点伤害便使[脉冲星飞雷]的冷却时间缩短1秒",
  },
  {
    id: "juno_p8",
    hero: "juno",
    name: "超能治愈",
    eng_name: "Hyper Healer",
    description: "受[超能环域]影响的的盟友获得50点过量生命值",
  },
  {
    id: "juno_p9",
    hero: "juno",
    name: "集结环域",
    eng_name: "Rally Ring",
    description: "当一名盟友穿过[超能环域]，[超能环域]的冷却时间缩短1秒",
  },
  {
    id: "juno_p10",
    hero: "juno",
    name: "黑洞",
    eng_name: "Black Hole",
    description: "[超能环域]使从其中穿过敌人的移动速度减慢35%，持续1秒",
  },
  {
    id: "juno_p11",
    hero: "juno",
    name: "星之所向",
    eng_name: "Stellar Focus",
    description: "[轨道射线]会跟随你，且持续时间增加35%",
  },
  {
    id: "juno_p12",
    hero: "juno",
    name: "轨道校准",
    eng_name: "Orbital Alignment",
    description: "在[轨道射线]内的敌人移动速度减慢35%，盟友移动速度加快25%",
  },
]

// 修改 TEST_ITEMS 数组，确保包含所有物品数据
// 将 TEST_ITEMS 数组替换为以下内容，包含所有物品数据

const TEST_ITEMS: Item[] = [
  {
    id: "item_g_w_c1",
    name: "武器润滑油",
    eng_name: "Weapon Grease",
    description: "+5%攻击速度",
    cost: 1000,
    category: "weapon",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_w_c2",
    name: "等离子转换器",
    eng_name: "Plasma Converter",
    description: "+10%武器吸血",
    cost: 1000,
    category: "weapon",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_w_c3",
    name: "补偿器",
    eng_name: "Compensator",
    description: "+5%武器强度",
    cost: 1000,
    category: "weapon",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_w_c4",
    name: "弹药储备",
    eng_name: "Ammo Reserves",
    description: "+20%弹匣容量",
    cost: 1500,
    category: "weapon",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_w_c5",
    name: "狂热增幅器",
    eng_name: "Frenzy Amplifier",
    description: "消灭敌人后获得10%攻击速度和移动速度，持续3秒",
    cost: 1500,
    category: "weapon",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_a_c1",
    name: "充能镀层",
    eng_name: "Charged Plating",
    description: "消耗终极技能充能后，本回合增加25护甲和10%技能强度",
    cost: 1000,
    category: "ability",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_a_c2",
    name: "强力战术集",
    eng_name: "Power Playbook",
    description: "+10%技能强度",
    cost: 1000,
    category: "ability",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_a_c3",
    name: "遮阳墨镜",
    eng_name: "Shady Spectacles",
    description: "+10%技能吸血",
    cost: 1000,
    category: "ability",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_a_c4",
    name: "求胜之心",
    eng_name: "Winning Attitude",
    description: "+25生命值\n死亡时获得15%终极技能充能",
    cost: 1500,
    category: "ability",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_s_c1",
    name: "战地配给",
    eng_name: "Field Rations",
    description: "参与目标攻防期间每秒恢复8点生命值",
    cost: 1000,
    category: "survival",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_s_c2",
    name: "电解质",
    eng_name: "Electrolytes",
    description: "回合开始时，获得100点不可恢复的过量生命值",
    cost: 1000,
    category: "survival",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_s_c3",
    name: "疾奔跑鞋",
    eng_name: "Running Shoes",
    description: "+10生命值\n回合开始及第一次重生时，你获得20%移动速度，持续10秒，仅在未交战时生效",
    cost: 1000,
    category: "survival",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_s_c4",
    name: "肾上腺素注射剂",
    eng_name: "Adrenaline Shot",
    description: "+10生命值\n拾取医疗包时，获得50过量生命值，移动速度提升20%，持续5秒",
    cost: 1000,
    category: "survival",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_s_c5",
    name: "急救工具",
    eng_name: "First Aid Kit",
    description: "+25护盾\n脱离战斗后的生命恢复延迟减少33%",
    cost: 1500,
    category: "survival",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_s_c6",
    name: "虹吸手套",
    eng_name: "Siphon Gloves",
    description: "+25生命值\n使用[快速近战攻击]造成伤害时，恢复25点生命值",
    cost: 1500,
    category: "survival",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_s_c7",
    name: "铠甲背心",
    eng_name: "Armored Vest",
    description: "+25护甲",
    cost: 1500,
    category: "survival",
    rarity: "common",
    hero: "general",
  },
  {
    id: "item_g_w_r1",
    name: "市售撞针",
    eng_name: "Aftermarket Firing Pin",
    description: "+10%攻击速度\n+5%移动速度",
    cost: 3750,
    category: "weapon",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_w_r2",
    name: "军火储备",
    eng_name: "Stockpile",
    description: "+5%攻击速度\n+25%弹匣容量",
    cost: 4000,
    category: "weapon",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_w_r3",
    name: "护盾瓦解器",
    eng_name: "Shieldbuster",
    description: "+5%武器强度\n对护盾或护甲造成伤害时，攻击速度提升15%，持续1秒",
    cost: 4000,
    category: "weapon",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_w_r4",
    name: "高级纳米生物",
    eng_name: "Advanced Nanobiotics",
    description: "+5%武器强度\n治疗友军后攻击速度增加10%，持续3秒",
    cost: 4000,
    category: "weapon",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_w_r5",
    name: "汲血技术",
    eng_name: "Technoleech",
    description: "+5%武器强度\n+10%武器吸血",
    cost: 4500,
    category: "weapon",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_w_r6",
    name: "极寒冷却剂",
    eng_name: "Icy Coolant",
    description: "+10%武器强度\n+5%冷却缩减",
    cost: 5000,
    category: "weapon",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_w_r7",
    name: "黑爪改造模块",
    eng_name: "Talon Modification Module",
    description: "+15%武器强度",
    cost: 5500,
    category: "weapon",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_a_r1",
    name: "定制储备",
    eng_name: "Custom Stock",
    description: "+5%武器强度\n+10%技能强度",
    cost: 3750,
    category: "ability",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_a_r2",
    name: "渣客怪玩意",
    eng_name: "Junker Watchamajig",
    description: "+25%初始大招充能",
    cost: 4000,
    category: "ability",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_a_r3",
    name: "生物光束溢流",
    eng_name: "Biolight Overflow",
    description: "+25生命值\n+5%技能强度\n使用大招后，周围的友军获得50过量生命值，持续3秒",
    cost: 4000,
    category: "ability",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_a_r4",
    name: "能量腕甲",
    eng_name: "Energized Bracers",
    description: "+10%技能强度\n+10%技能吸血",
    cost: 4000,
    category: "ability",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_a_r5",
    name: "运动护腕",
    eng_name: "Wrist Wraps",
    description: "+5%技能强度\n+10%攻击速度",
    cost: 4000,
    category: "ability",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_a_r6",
    name: "万能工具",
    eng_name: "Multi-Tool",
    description: "+5%技能强度\n+10%冷却缩减",
    cost: 5000,
    category: "ability",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_a_r7",
    name: "纳米可乐",
    eng_name: "Nano Cola",
    description: "+20%技能强度",
    cost: 5500,
    category: "ability",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_s_r1",
    name: "强化钛镀层",
    eng_name: "Reinforced Titanium",
    description: "+25护盾\n护盾未耗尽时，受到的技能伤害减少15%",
    cost: 3750,
    category: "survival",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_s_r2",
    name: "维ET恤",
    eng_name: "Vital-e-tee",
    description: "+10护甲\n将100点生命值转化为护甲",
    cost: 4000,
    category: "survival",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_s_r3",
    name: "缓冲衬垫",
    eng_name: "Cushioned Padding",
    description: "+25护盾\n-40%自身负面效果持续时间\n被击晕、麻醉、限制时，在3秒内恢复10%生命值",
    cost: 4000,
    category: "survival",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_s_r4",
    name: "费斯卡聚光器",
    eng_name: "Vishkar Condensor",
    description: "+25护盾\n将100生命值转化为护盾",
    cost: 4000,
    category: "survival",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_s_r5",
    name: "铁甲排气口",
    eng_name: "Ironclad Exhaust Ports",
    description: "+5%冷却缩减\n任意技能进入冷却时，获得25点过量生命值，持续3秒",
    cost: 4000,
    category: "survival",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_s_r6",
    name: "十字军液压器",
    eng_name: "Crusader Hydraulics",
    description: "+25护甲\n护甲未耗尽时，受到的武器伤害减少10%",
    cost: 4500,
    category: "survival",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_s_r7",
    name: "铁眼",
    eng_name: "Iron Eyes",
    description: "+25护盾\n你受到的暴击伤害减少20%",
    cost: 4500,
    category: "survival",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_s_r8",
    name: "Z系列机甲",
    eng_name: "Meka Z-Series",
    description: "+8%生命、护盾、护甲",
    cost: 5000,
    category: "survival",
    rarity: "rare",
    hero: "general",
  },
  {
    id: "item_g_w_e1",
    name: "代码破译器",
    eng_name: "Codebreaker",
    description: "+15%武器强度\n无视50%的护甲减伤",
    cost: 9000,
    category: "weapon",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_w_e2",
    name: "可回收弹头",
    eng_name: "Salvaged Slugs",
    description: "+10%攻击速度\n+30%对屏障伤害\n对屏障造成武器伤害时，有50%几率恢复1发弹药",
    cost: 9500,
    category: "weapon",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_w_e3",
    name: "沃斯卡娅军械",
    eng_name: "Volskaya Ordnance",
    description: "+10%攻击速度\n敌人的生命值上限每比你多100点，你对其造成的武器伤害增加5%，最多20%",
    cost: 9500,
    category: "weapon",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_w_e4",
    name: "指挥官的弹夹",
    eng_name: "Commander's Clip",
    description: "+10%攻击速度\n+40%弹匣容量\n使用技能后，自动装填弹匣的10%",
    cost: 10000,
    category: "weapon",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_w_e5",
    name: "武器干扰弹",
    eng_name: "Weapon Jammer",
    description: "+25护甲\n+10%武器强度\n造成武器伤害时，目标的攻击速度加成下降10%，你的攻击速度增加10%，持续2秒",
    cost: 10000,
    category: "weapon",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_w_e6",
    name: "助推喷气背包",
    eng_name: "Booster Jets",
    description: "+20%攻击速度\n使用技能后，移动速度增加20%，持续2秒",
    cost: 11000,
    category: "weapon",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_w_e7",
    name: "艾玛莉的解毒剂",
    eng_name: "Amari's Antidote",
    description: "+25生命值\n+15%武器强度\n使用武器治疗生命值低于50%的友军时，治疗量增加15%",
    cost: 11000,
    category: "weapon",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_w_e8",
    name: "闪电部队抑制器",
    eng_name: "El-Sa'ka Suppressor",
    description: "+10%武器强度\n使用武器对敌人造成暴击伤害时，目标受到的治疗减少30%，持续3秒",
    cost: 11000,
    category: "weapon",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_w_e9",
    name: "高强度光子加速器",
    eng_name: "Hardlight Accelerator",
    description: "+10%武器强度\n+10%冷却缩减\n使用技能后，获得5%武器强度，持续3秒，最多叠加3次",
    cost: 11000,
    category: "weapon",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_w_e10",
    name: "断魂曲",
    eng_name: "The Closer",
    description: "+20%武器强度\n+10%暴击伤害\n暴击伤害使敌人显示位置3秒",
    cost: 13000,
    category: "weapon",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_w_e11",
    name: "蜘蛛之眼",
    eng_name: "Eye of the Spider",
    description: "+25%武器强度\n对生命值低于30%的敌人额外造成10%伤害",
    cost: 13500,
    category: "weapon",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_a_e1",
    name: "三连发汤姆逊冲锋枪",
    eng_name: "Three-tap Tommygun",
    description: "+10%技能强度\n+10%攻击速度\n使用技能后，你的后3次武器伤害对目标造成其3%生命值的额外伤害",
    cost: 9500,
    category: "ability",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_a_e2",
    name: "催化水晶",
    eng_name: "Catalytic Crystal",
    description: "+15%技能强度\n技能伤害和治疗提供额外20%的终极技能充能",
    cost: 10000,
    category: "ability",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_a_e3",
    name: "光明科创聚变驱动器",
    eng_name: "LumériCo Fusion Drive",
    description: "+50护甲\n+15%技能强度\n使用技能后，在2秒内持续恢复50护甲或护盾",
    cost: 10000,
    category: "ability",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_a_e4",
    name: "生物技术极限优化器",
    eng_name: "Biotech Maximizer",
    description: "+25生命值\n+10%技能强度\n使用治疗技能时，每治疗一名盟友，返还5%的冷却时间",
    cost: 10000,
    category: "ability",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_a_e5",
    name: "超级屈肌",
    eng_name: "Superflexor",
    description: "+25生命值\n+10%武器强度\n使用武器造成伤害或治疗时，获得5%技能强度，持续3秒，最多叠加5次",
    cost: 10000,
    category: "ability",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_a_e6",
    name: "赛博毒液",
    eng_name: "Cybervenom",
    description: "+10%技能强度\n+5%冷却缩减\n使用技能对敌人造成伤害时，目标受到的治疗减少30%，持续3秒",
    cost: 10500,
    category: "ability",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_a_e7",
    name: "低温液氮",
    eng_name: "Liquid Nitrogen",
    description: "+25生命值\n+10%技能强度\n对敌人造成技能伤害时，降低其移动速度20%，持续3秒",
    cost: 11000,
    category: "ability",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_a_e8",
    name: "灵狐印记",
    eng_name: "Mark of the Kitsune",
    description: "+10%技能强度\n使用技能后，你下一次使用武器时对目标造成25点额外伤害或治疗",
    cost: 11000,
    category: "ability",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_a_e9",
    name: "璀璨智瞳",
    eng_name: "Iridescent Iris",
    description: "+20%技能强度\n+10%冷却缩减\n使用大招后，获得100过量生命值，持续3秒",
    cost: 11000,
    category: "ability",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_a_e10",
    name: "冠军之选",
    eng_name: "Champion's Kit",
    description: "+40%技能强度",
    cost: 13500,
    category: "ability",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_s_e1",
    name: "基因科学家的药瓶",
    eng_name: "Geneticist's Vial",
    description: "+25生命值\n每回合第一次阵亡时，在3秒后原地复活，但只有200点生命值",
    cost: 9000,
    category: "survival",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_s_e2",
    name: "神圣干涉",
    eng_name: "Divine Intervention",
    description: "+50护盾\n单次受到100点以上伤害时，立即激活护盾回复，并治疗所受伤害的15%",
    cost: 9500,
    category: "survival",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_s_e3",
    name: "血债血偿",
    eng_name: "Bloodbound",
    description: "+50生命值\n上次击杀你的敌人在你附近时，显示其位置，你对其造成10%额外伤害并享受10%伤害减免",
    cost: 9500,
    category: "survival",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_s_e4",
    name: "黑暗拳套",
    eng_name: "Gloomgauntlet",
    description: "+50护甲\n+15%近战伤害\n造成近战伤害时，在2秒内恢复5%生命，并增加10%移动速度",
    cost: 9500,
    category: "survival",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_s_e5",
    name: "威尔海姆重甲",
    eng_name: "Rustung Von Wilhelm",
    description: "+15%生命、护盾、护甲\n生命值低于30%时，获得10%伤害减免",
    cost: 10000,
    category: "survival",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_s_e6",
    name: "幻影乱流",
    eng_name: "Phantasmic Flux",
    description: "+10%技能强度\n+10%武器强度\n+15%技能吸血\n+15%武器吸血\n过量吸血转化为最多100点的过量生命值",
    cost: 10000,
    category: "survival",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_s_e7",
    name: "火星愈疗器",
    eng_name: "Martian Mender",
    description: "+25生命值\n+10%冷却缩减\n每秒恢复3%生命值",
    cost: 10000,
    category: "survival",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_s_e8",
    name: "钒素注射",
    eng_name: "Vanadium Injection",
    description:
      "+50护盾\n终极技能充能达到100%时，获得：\n　50生命值\n　10%武器强度\n　10%技能强度\n　10%攻击速度\n　10%冷却缩减\n　10%移动速度",
    cost: 10000,
    category: "survival",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_s_e9",
    name: "奥古迪姆减伤力场",
    eng_name: "Ogundimu Reduction Field",
    description: "+50护甲\n受到伤害时获得0.5%减伤，持续1秒，最多叠加20次",
    cost: 11000,
    category: "survival",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_g_s_e10",
    name: "星云导体",
    eng_name: "Nebula Conduit",
    description: "+50生命值\n+10%武器强度\n将所受伤害的15%分摊到此后3秒内",
    cost: 11000,
    category: "survival",
    rarity: "epic",
    hero: "general",
  },
  {
    id: "item_s76_w_r1",
    name: "脉冲转换器",
    eng_name: "Pulse Converter",
    description: "+5%攻击速度\n+5%冷却缩减\n使用[螺旋飞弹]后，恢复20%弹药",
    cost: 4000,
    category: "weapon",
    rarity: "rare",
    hero: "soldier76",
  },
  {
    id: "item_s76_a_r1",
    name: "减爆战靴",
    eng_name: "Bomb Diffusal Boots",
    description: "+25生命值\n+5%武器强度\n[螺旋飞弹]对自身伤害降低90%，对自身击退增加200%",
    cost: 4000,
    category: "ability",
    rarity: "rare",
    hero: "soldier76",
  },
  {
    id: "item_s76_a_r2",
    name: "电池模块",
    eng_name: "Battery Pack",
    description: "+10%技能强度\n+30%[生物力场]持续时间",
    cost: 4000,
    category: "ability",
    rarity: "rare",
    hero: "soldier76",
  },
  {
    id: "item_s76_s_r1",
    name: "疲劳抑制剂",
    eng_name: "Compression Fatigues",
    description: "+25生命值\n+5%武器强度\n+25%[疾跑]移动速度",
    cost: 4000,
    category: "survival",
    rarity: "rare",
    hero: "soldier76",
  },
  {
    id: "item_s76_a_e1",
    name: "快速反应半径",
    eng_name: "Rapid Response Radius",
    description: "+10%技能强度\n+30%[生物力场]半径\n[生物力场]对生命值低于50%的盟友额外造成10%的治疗",
    cost: 10000,
    category: "ability",
    rarity: "epic",
    hero: "soldier76",
  },
  {
    id: "item_s76_a_e2",
    name: "终局均衡器",
    eng_name: "Endgame Equalizer",
    description: "+25生命值\n+15%技能强度\n消耗充能使用[战术目镜]时刷新所有技能，[战术目镜]激活期间获得20%冷却缩减",
    cost: 10000,
    category: "ability",
    rarity: "epic",
    hero: "soldier76",
  },
  {
    id: "item_s76_s_e1",
    name: "铁肺",
    eng_name: "Iron Lung",
    description: "+25生命值\n[疾跑]期间每秒获得最大生命值5%的过量生命值，持续5秒，最多获得25%",
    cost: 10000,
    category: "survival",
    rarity: "epic",
    hero: "soldier76",
  },
  {
    id: "item_juno_w_r1",
    name: "优势射击",
    eng_name: "Vantage Shot",
    description: "+5%武器强度\n[医疗冲击枪]在空中对敌人造成额外15%伤害",
    cost: 4000,
    category: "weapon",
    rarity: "rare",
    hero: "juno",
  },
  {
    id: "item_juno_a_r1",
    name: "锁定护盾",
    eng_name: "Lock On Shield",
    description: "+10%技能强度\n引导[脉冲星飞雷]期间，获得相当于50%护盾值的过量生命值",
    cost: 4000,
    category: "ability",
    rarity: "rare",
    hero: "juno",
  },
  {
    id: "item_juno_a_r2",
    name: "勒克斯环",
    eng_name: "Lux Loop",
    description: "+10%技能强度\n+25%[超能环域]持续时间",
    cost: 4000,
    category: "ability",
    rarity: "rare",
    hero: "juno",
  },
  {
    id: "item_juno_s_r1",
    name: "助推火箭",
    eng_name: "Boosted Rockets",
    description: "+25护盾\n+25%[滑翔推进]持续时间",
    cost: 4000,
    category: "survival",
    rarity: "rare",
    hero: "juno",
  },
  {
    id: "item_juno_w_e1",
    name: "脉冲尖峰",
    eng_name: "Pulse Spike",
    description: "+10%攻击速度\n+35%[脉冲星飞雷]投射物速度\n发射[脉冲星飞雷]后，攻击速度提升25%，持续4秒",
    cost: 10000,
    category: "weapon",
    rarity: "epic",
    hero: "juno",
  },
  {
    id: "item_juno_w_e2",
    name: "远程冲击枪",
    eng_name: "Long Range Blaster",
    description: "+15%武器强度\n[医疗冲击枪]对12米外的目标额外造成15%伤害和治疗",
    cost: 12000,
    category: "weapon",
    rarity: "epic",
    hero: "juno",
  },
  {
    id: "item_juno_w_e3",
    name: "重力猛推",
    eng_name: "Gravitational Push",
    description: "+15%武器强度\n[滑翔推进]期间获得20%攻击速度，且[快速近战攻击]拥有击退效果",
    cost: 10000,
    category: "weapon",
    rarity: "epic",
    hero: "juno",
  },
  {
    id: "item_juno_a_e1",
    name: "脉冲星毁灭者",
    eng_name: "Pulstar Destroyers",
    description: "+15%技能强度\n[脉冲星飞雷]额外造成20点爆炸伤害",
    cost: 10000,
    category: "ability",
    rarity: "epic",
    hero: "juno",
  },
  {
    id: "item_juno_a_e2",
    name: "太阳能护盾",
    eng_name: "Solar Shielding",
    description: "+25%技能强度\n朱诺或盟友拥有[超能环域]的效果时，每秒恢复25护盾",
    cost: 10000,
    category: "ability",
    rarity: "epic",
    hero: "juno",
  },
  {
    id: "item_juno_a_e3",
    name: "朱诺计划调节器",
    eng_name: "Red Promise Regulator",
    description: "+50护盾\n+15%技能强度\n使用[轨道射线]时刷新所有技能",
    cost: 10000,
    category: "ability",
    rarity: "epic",
  },
  {
    id: "item_juno_s_e1",
    name: "强固滑翔",
    eng_name: "Forti-Glide",
    description: "+75护盾\n[滑翔推进]期间获得10%伤害减免",
    cost: 10000,
    category: "survival",
    rarity: "epic",
    hero: "juno",
  },
  {
    id: "item_juno_s_e2",
    name: "日晖血清",
    eng_name: "Sunburst Serum",
    description: "+75护盾\n[轨道射线]的治疗效果提升25%",
    cost: 10000,
    category: "survival",
    rarity: "epic",
    hero: "juno",
  },
]

export const loadHeroPowers = () => TEST_HERO_POWERS
export const loadItems = () => TEST_ITEMS
