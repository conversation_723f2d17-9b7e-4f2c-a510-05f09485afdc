// 定义配置数据结构
export interface BDConfig {
  hero: string
  powers: (string | null)[]
  items: Array<Array<{ itemId: string; priority: string } | null>>
  version: string // 用于未来版本兼容性
}

// 物品ID映射表（用数字代替长字符串）
const ITEM_MAP: Record<string, string> = {
  'item_g_s_c7': '1', 'item_g_a_c2': '2', 'item_g_w_c3': '3', 'item_g_a_c3': '4',
  'item_g_a_c1': '5', 'item_g_s_c2': '6', 'item_g_a_r7': '7', 'item_juno_a_r1': '8',
  'item_g_s_c5': '9', 'item_juno_a_e1': 'a', 'item_g_a_r6': 'b', 'item_juno_s_r1': 'c',
  'item_g_a_e10': 'd', 'item_g_a_e6': 'e', 'item_s76_a_r1': 'f', 'item_s76_a_e1': 'g',
  'item_s76_s_r1': 'h', 'item_g_w_c1': 'i', 'item_g_w_c2': 'j', 'item_g_w_r1': 'k'
}

// 反向映射
const REVERSE_ITEM_MAP: Record<string, string> = {}
Object.entries(ITEM_MAP).forEach(([key, value]) => {
  REVERSE_ITEM_MAP[value] = key
})

// 超级压缩格式
function compressToUltraShort(config: BDConfig): string {
  // 格式: H,P1P2P3,I1I2I3...,V
  // H: 英雄 (0=soldier76, 1=juno)
  // P: 异能位置+ID (如：04表示位置0异能4)
  // I: 物品 (回合+槽位+ID+优先级，如：001m表示回合0槽位0物品1优先级must)

  const hero = config.hero === 'juno' ? '1' : '0'

  // 压缩异能
  let powers = ''
  config.powers.forEach((power, index) => {
    if (power) {
      const id = power.match(/p(\d+)$/)?.[1] || '0'
      powers += index + id
    }
  })

  // 压缩物品
  let items = ''
  config.items.forEach((round, roundIndex) => {
    round.forEach((item, slotIndex) => {
      if (item) {
        const itemCode = ITEM_MAP[item.itemId] || 'z'
        const priorityCode = item.priority === 'must' ? 'm' :
                           item.priority === 'affordable' ? 'a' : 'o'
        items += roundIndex.toString() + slotIndex.toString() + itemCode + priorityCode
      }
    })
  })

  return `${hero},${powers},${items},1`
}

// 解压超级压缩格式
function decompressFromUltraShort(ultraShort: string): BDConfig {
  const parts = ultraShort.split(',')
  if (parts.length !== 4) throw new Error('格式错误')

  const [heroCode, powersStr, itemsStr] = parts

  // 还原英雄
  const hero = heroCode === '1' ? 'juno' : 'soldier76'

  // 还原异能
  const powers: (string | null)[] = [null, null, null, null]
  for (let i = 0; i < powersStr.length; i += 2) {
    const index = parseInt(powersStr[i])
    const id = powersStr[i + 1]
    const prefix = hero === 'juno' ? 'juno_p' : 's76_p'
    powers[index] = prefix + id
  }

  // 还原物品
  const items: Array<Array<{ itemId: string; priority: string } | null>> =
    Array(7).fill(null).map(() => Array(6).fill(null))

  for (let i = 0; i < itemsStr.length; i += 4) {
    const roundIndex = parseInt(itemsStr[i])
    const slotIndex = parseInt(itemsStr[i + 1])
    const itemCode = itemsStr[i + 2]
    const priorityCode = itemsStr[i + 3]

    const itemId = REVERSE_ITEM_MAP[itemCode] || 'item_unknown'
    const priority = priorityCode === 'm' ? 'must' :
                    priorityCode === 'a' ? 'affordable' : 'optional'

    items[roundIndex][slotIndex] = { itemId, priority }
  }

  return {
    hero,
    powers,
    items,
    version: '1.0'
  }
}

// 压缩配置为超短格式（备用方案）
function compressToShort(config: BDConfig): string {
  const hero = config.hero === 'juno' ? 'j' : 's'

  const powers: string[] = []
  config.powers.forEach((power, index) => {
    if (power) {
      const id = power.match(/p(\d+)$/)?.[1] || '0'
      powers.push(`${index}:${id}`)
    }
  })

  const items: string[] = []
  config.items.forEach((round, roundIndex) => {
    round.forEach((item, slotIndex) => {
      if (item) {
        let shortId = item.itemId.replace('item_', '')
        if (shortId.startsWith('juno_')) {
          shortId = 'j.' + shortId.substring(5)
        } else if (shortId.startsWith('s76_')) {
          shortId = 's.' + shortId.substring(4)
        } else if (shortId.startsWith('g_')) {
          shortId = 'g.' + shortId.substring(2)
        }
        shortId = shortId.replace(/_/g, '.')

        const priority = item.priority.charAt(0)
        items.push(`${roundIndex}.${slotIndex}:${shortId}.${priority}`)
      }
    })
  })

  return `${hero}|${powers.join(',')}|${items.join(',')}|1`
}

// 解压缩短格式
function decompressFromShort(shortString: string): BDConfig {
  const parts = shortString.split('|')
  if (parts.length !== 4) throw new Error('格式错误')

  const [heroCode, powersStr, itemsStr] = parts

  // 还原英雄
  const hero = heroCode === 'j' ? 'juno' : 'soldier76'

  // 还原异能
  const powers: (string | null)[] = [null, null, null, null]
  if (powersStr) {
    powersStr.split(',').forEach(powerStr => {
      const [indexStr, idStr] = powerStr.split(':')
      const index = parseInt(indexStr)
      const prefix = hero === 'juno' ? 'juno_p' : 's76_p'
      powers[index] = prefix + idStr
    })
  }

  // 还原物品
  const items: Array<Array<{ itemId: string; priority: string } | null>> =
    Array(7).fill(null).map(() => Array(6).fill(null))

  if (itemsStr) {
    itemsStr.split(',').forEach(itemStr => {
      const [posStr, itemData] = itemStr.split(':')
      const [roundIndex, slotIndex] = posStr.split('.').map(Number)
      const parts = itemData.split('.')
      const priority = parts.pop() // 最后一个是优先级

      // 还原物品ID
      let itemId = 'item_'
      if (parts[0] === 'j') {
        itemId += 'juno_' + parts.slice(1).join('_')
      } else if (parts[0] === 's') {
        itemId += 's76_' + parts.slice(1).join('_')
      } else if (parts[0] === 'g') {
        itemId += 'g_' + parts.slice(1).join('_')
      }

      const priorityMap: Record<string, string> = { m: 'must', a: 'affordable', o: 'optional' }

      items[roundIndex][slotIndex] = {
        itemId,
        priority: priorityMap[priority!] || 'optional'
      }
    })
  }

  return {
    hero,
    powers,
    items,
    version: '1.0'
  }
}

// Brotli压缩函数（浏览器环境）
async function compressBrotli(data: string): Promise<string> {
  if (typeof CompressionStream !== 'undefined') {
    // 现代浏览器支持
    const stream = new CompressionStream('gzip') // 浏览器通常支持gzip，Brotli支持有限
    const writer = stream.writable.getWriter()
    const reader = stream.readable.getReader()

    writer.write(new TextEncoder().encode(data))
    writer.close()

    const chunks: Uint8Array[] = []
    let done = false

    while (!done) {
      const { value, done: readerDone } = await reader.read()
      done = readerDone
      if (value) chunks.push(value)
    }

    const compressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0))
    let offset = 0
    for (const chunk of chunks) {
      compressed.set(chunk, offset)
      offset += chunk.length
    }

    return btoa(String.fromCharCode(...compressed))
  } else {
    // 降级到普通Base64
    return btoa(data)
  }
}

// 导出配置为字符串
export function exportConfig(config: BDConfig): string {
  try {
    // 优先使用超级压缩格式
    const ultraShort = compressToUltraShort(config)
    const ultraResult = 'US:' + btoa(ultraShort)

    // 如果超级压缩短于100字符，直接使用
    if (ultraResult.length < 100) {
      return ultraResult
    }

    // 否则尝试自定义压缩格式
    const shortString = compressToShort(config)
    const customResult = 'CP:' + btoa(shortString)

    if (customResult.length < 100) {
      return customResult
    }

    // 最后使用字符串压缩
    const jsonString = JSON.stringify(config)
    let compressed = jsonString
      .replace(/"itemId"/g, '"i"')
      .replace(/"priority"/g, '"p"')
      .replace(/"must"/g, '"m"')
      .replace(/"affordable"/g, '"a"')
      .replace(/"optional"/g, '"o"')
      .replace(/item_g_/g, 'g_')
      .replace(/item_juno_/g, 'j_')
      .replace(/item_s76_/g, 's_')
      .replace(/juno_p/g, 'jp')
      .replace(/s76_p/g, 'sp')

    const gzResult = 'GZ:' + btoa(compressed)

    // 返回最短的格式
    const results = [ultraResult, customResult, gzResult]
    return results.reduce((shortest, current) =>
      current.length < shortest.length ? current : shortest
    )
  } catch (error) {
    console.error("导出配置失败:", error)
    throw new Error("导出配置失败")
  }
}

// 从字符串导入配置
export function importConfig(configString: string): BDConfig {
  try {
    // 检查格式前缀
    if (configString.startsWith('US:')) {
      // 超级压缩格式
      const decodedString = atob(configString.substring(3))
      return decompressFromUltraShort(decodedString)
    } else if (configString.startsWith('CP:')) {
      // 自定义压缩格式
      const decodedString = atob(configString.substring(3))
      return decompressFromShort(decodedString)
    } else if (configString.startsWith('GZ:')) {
      // 字符串压缩格式
      const compressed = atob(configString.substring(3))

      // 还原字符串替换
      const jsonString = compressed
        .replace(/"i"/g, '"itemId"')
        .replace(/"p"/g, '"priority"')
        .replace(/"m"/g, '"must"')
        .replace(/"a"/g, '"affordable"')
        .replace(/"o"/g, '"optional"')
        .replace(/g_/g, 'item_g_')
        .replace(/j_/g, 'item_juno_')
        .replace(/s_/g, 'item_s76_')
        .replace(/jp/g, 'juno_p')
        .replace(/sp/g, 's76_p')

      const config = JSON.parse(jsonString) as BDConfig

      // 验证配置格式
      if (!config.hero || !Array.isArray(config.powers) || !Array.isArray(config.items)) {
        throw new Error("配置格式无效")
      }

      return config
    } else {
      // 传统格式：直接Base64 JSON或包含分隔符的压缩格式
      const decodedString = atob(configString)

      if (decodedString.includes(',') && decodedString.split(',').length === 4) {
        // 超级压缩格式（无前缀）
        return decompressFromUltraShort(decodedString)
      } else if (decodedString.includes('|')) {
        // 自定义压缩格式（无前缀）
        return decompressFromShort(decodedString)
      } else {
        // 传统JSON格式
        const config = JSON.parse(decodedString) as BDConfig

        // 验证配置格式
        if (!config.hero || !Array.isArray(config.powers) || !Array.isArray(config.items)) {
          throw new Error("配置格式无效")
        }

        return config
      }
    }
  } catch (error) {
    console.error("导入配置失败:", error)
    throw new Error("导入配置失败，请检查代码格式是否正确")
  }
}
