"use client"

import { useState } from "react"
import { getPowerImagePath } from "../utils/data-loader"

interface PowerImageProps {
  powerId: string
  name: string
  className?: string
  size?: number
  fallbackText?: string
}

export default function PowerImage({ powerId, name, className = "", size = 40, fallbackText }: PowerImageProps) {
  const [imageError, setImageError] = useState(false)

  // 确保powerId是正确的格式，移除可能的前缀
  const cleanPowerId = powerId.replace(/^power_/, "")

  // 获取图片URL
  const imageSrc = getPowerImagePath(cleanPowerId)

  // 处理图片加载错误
  const handleImageError = () => {
    // 添加更详细的日志
    console.log(`异能图片加载失败: ${imageSrc} (ID: ${powerId}, 英雄: ${powerId.includes("s76") ? "士兵76" : "朱诺"})`)

    // 设置图片错误状态
    setImageError(true)
  }

  // 如果图片加载失败或没有图片，显示首字母
  if (imageError) {
    return (
      <div
        className={`flex items-center justify-center ${powerId.includes("s76") ? "bg-blue-800" : "bg-blue-900"} ${className}`}
        style={{ width: size, height: size }}
      >
        <span className="text-lg font-bold text-white">{fallbackText || name.charAt(0)}</span>
      </div>
    )
  }

  return (
    <div className={`overflow-hidden ${className}`} style={{ width: size, height: size }}>
      <img
        src={imageSrc || "/placeholder.svg"}
        alt={name}
        className="w-full h-full object-cover"
        onError={handleImageError}
        width={size}
        height={size}
        crossOrigin="anonymous"
      />
    </div>
  )
}
