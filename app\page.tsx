"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Search, Plus, Sword, Crown, Trophy, Users, Zap, Shield } from "lucide-react"
import Link from "next/link"

export default function HomePage() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Overwatch-style Geometric Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-300 via-blue-200 to-purple-200">
        {/* Large geometric shapes */}
        <div className="absolute inset-0">
          {/* Main diagonal shapes */}
          <div className="absolute top-0 left-0 w-full h-full">
            <svg className="w-full h-full" viewBox="0 0 1920 1080" preserveAspectRatio="xMidYMid slice">
              <defs>
                <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style={{stopColor: '#e2e8f0', stopOpacity: 0.8}} />
                  <stop offset="50%" style={{stopColor: '#cbd5e1', stopOpacity: 0.6}} />
                  <stop offset="100%" style={{stopColor: '#94a3b8', stopOpacity: 0.4}} />
                </linearGradient>
                <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style={{stopColor: '#dbeafe', stopOpacity: 0.7}} />
                  <stop offset="100%" style={{stopColor: '#bfdbfe', stopOpacity: 0.5}} />
                </linearGradient>
                <linearGradient id="grad3" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style={{stopColor: '#e0e7ff', stopOpacity: 0.6}} />
                  <stop offset="100%" style={{stopColor: '#c7d2fe', stopOpacity: 0.4}} />
                </linearGradient>
              </defs>

              {/* Large diagonal triangles */}
              <polygon points="0,0 800,0 400,600" fill="url(#grad1)" />
              <polygon points="1920,0 1920,400 1200,800" fill="url(#grad2)" />
              <polygon points="0,1080 600,1080 300,500" fill="url(#grad3)" />
              <polygon points="1920,1080 1920,600 1400,1080" fill="url(#grad1)" />

              {/* Medium triangles */}
              <polygon points="600,200 1000,200 800,600" fill="url(#grad2)" opacity="0.6" />
              <polygon points="1200,400 1600,400 1400,800" fill="url(#grad3)" opacity="0.5" />
              <polygon points="200,700 600,700 400,1080" fill="url(#grad1)" opacity="0.4" />

              {/* Small accent triangles */}
              <polygon points="300,100 500,100 400,300" fill="#f97316" opacity="0.3" />
              <polygon points="1500,200 1700,200 1600,400" fill="#3b82f6" opacity="0.3" />
              <polygon points="100,800 300,800 200,1000" fill="#eab308" opacity="0.2" />
              <polygon points="1600,700 1800,700 1700,900" fill="#06b6d4" opacity="0.2" />
            </svg>
          </div>

          {/* Floating particles/dots */}
          <div className="absolute inset-0">
            <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-orange-400 rounded-full opacity-60 animate-pulse"></div>
            <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-blue-500 rounded-full opacity-50 animate-pulse" style={{animationDelay: '1s'}}></div>
            <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-yellow-400 rounded-full opacity-40 animate-pulse" style={{animationDelay: '2s'}}></div>
            <div className="absolute top-2/3 right-1/4 w-1 h-1 bg-cyan-400 rounded-full opacity-50 animate-pulse" style={{animationDelay: '0.5s'}}></div>
            <div className="absolute bottom-1/3 right-2/3 w-2 h-2 bg-purple-400 rounded-full opacity-30 animate-pulse" style={{animationDelay: '1.5s'}}></div>
          </div>
        </div>
      </div>

      {/* Main Arena */}
      <div className="relative overflow-hidden">
        <div className="relative z-10 container mx-auto px-4 py-16">
          {/* Arena Header */}
          <div className="text-center mb-16">
            <div className="flex items-center justify-center mb-8">
              <div className="w-20 h-20 bg-gradient-to-br from-orange-500 via-yellow-500 to-blue-600 rounded-full flex items-center justify-center mr-6 shadow-2xl border-4 border-white/30">
                <Crown className="w-10 h-10 text-white" />
              </div>
              <div className="text-right">
                <h1 className="text-6xl md:text-8xl font-bold bg-gradient-to-r from-orange-600 via-orange-500 to-blue-600 bg-clip-text text-transparent drop-shadow-lg">
                  角斗领域
                </h1>
                <div className="text-lg md:text-xl text-gray-700 font-semibold tracking-wider">
                  OVERWATCH STADIUM
                </div>
              </div>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 bg-clip-text text-transparent mb-6 drop-shadow-sm">
              英雄构筑竞技场
            </h2>
            <p className="text-xl text-gray-800 max-w-4xl mx-auto leading-relaxed font-medium">
              踏入守望先锋的终极竞技场，在这里锻造您的传奇英雄构建。
              通过策略性的装备选择和异能搭配，在角斗场中证明您的实力，成为真正的冠军！
            </p>
          </div>

          {/* Arena Action Buttons */}
          <div className="flex flex-col md:flex-row gap-8 justify-center items-center mb-20">
            <Link href="/simulator">
              <Button className="group relative overflow-hidden bg-gradient-to-r from-orange-600 via-orange-500 to-orange-700 hover:from-orange-700 hover:via-orange-600 hover:to-orange-800 text-white px-10 py-8 text-2xl font-bold rounded-2xl shadow-2xl transform hover:scale-105 transition-all duration-300 min-w-[280px] border-2 border-yellow-400/40">
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-300 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                <Sword className="w-8 h-8 mr-4" />
                锻造构建
              </Button>
            </Link>

            <Link href="/search">
              <Button className="group relative overflow-hidden bg-gradient-to-r from-blue-600 via-blue-500 to-blue-700 hover:from-blue-700 hover:via-blue-600 hover:to-blue-800 text-white px-10 py-8 text-2xl font-bold rounded-2xl shadow-2xl transform hover:scale-105 transition-all duration-300 min-w-[280px] border-2 border-cyan-400/40">
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-300 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                <Search className="w-8 h-8 mr-4" />
                探索构建
              </Button>
            </Link>
          </div>

          {/* Arena Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
            <Card className="bg-white/60 border-2 border-orange-300/60 backdrop-blur-sm hover:bg-white/80 hover:border-orange-400/80 transition-all duration-300 group shadow-xl">
              <div className="p-8 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg border-2 border-white/50">
                  <Zap className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">异能之力</h3>
                <p className="text-gray-700 leading-relaxed">
                  解锁强大的英雄异能，每个异能都能彻底改变您的战斗策略，创造独一无二的游戏体验。
                </p>
              </div>
            </Card>

            <Card className="bg-white/60 border-2 border-blue-300/60 backdrop-blur-sm hover:bg-white/80 hover:border-blue-400/80 transition-all duration-300 group shadow-xl">
              <div className="p-8 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg border-2 border-white/50">
                  <Shield className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">装备精通</h3>
                <p className="text-gray-700 leading-relaxed">
                  精心搭配武器、技能和生存装备，通过策略性的物品选择打造完美的英雄构建。
                </p>
              </div>
            </Card>

            <Card className="bg-white/60 border-2 border-purple-300/60 backdrop-blur-sm hover:bg-white/80 hover:border-purple-400/80 transition-all duration-300 group shadow-xl">
              <div className="p-8 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg border-2 border-white/50">
                  <Trophy className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">竞技荣耀</h3>
                <p className="text-gray-700 leading-relaxed">
                  在角斗场中展示您的构建，与其他玩家切磋技艺，争夺最高荣誉和传奇地位。
                </p>
              </div>
            </Card>
          </div>
        </div>
      </div>

      {/* Arena Statistics */}
      <div className="bg-white/70 backdrop-blur-sm border-t-2 border-gray-300/60">
        <div className="container mx-auto px-4 py-16">
          <h3 className="text-3xl font-bold text-center text-gray-800 mb-12">竞技场数据统计</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="group">
              <div className="text-4xl md:text-5xl font-bold text-orange-600 mb-3 group-hover:scale-110 transition-transform duration-300">18+</div>
              <div className="text-gray-700 font-semibold">Stadium英雄</div>
            </div>
            <div className="group">
              <div className="text-4xl md:text-5xl font-bold text-blue-600 mb-3 group-hover:scale-110 transition-transform duration-300">80+</div>
              <div className="text-gray-700 font-semibold">装备道具</div>
            </div>
            <div className="group">
              <div className="text-4xl md:text-5xl font-bold text-cyan-600 mb-3 group-hover:scale-110 transition-transform duration-300">200+</div>
              <div className="text-gray-700 font-semibold">独特异能</div>
            </div>
            <div className="group">
              <div className="text-4xl md:text-5xl font-bold text-yellow-600 mb-3 group-hover:scale-110 transition-transform duration-300">∞</div>
              <div className="text-gray-700 font-semibold">构建组合</div>
            </div>
          </div>
        </div>
      </div>

      {/* Arena Footer */}
      <footer className="bg-gray-800/90 backdrop-blur-sm border-t-2 border-gray-600/50">
        <div className="container mx-auto px-4 py-12">
          <div className="text-center">
            <div className="flex items-center justify-center mb-6">
              <Crown className="w-8 h-8 text-orange-400 mr-3" />
              <h4 className="text-2xl font-bold text-white">角斗领域</h4>
            </div>
            <p className="text-gray-300 text-lg mb-4">
              在守望先锋角斗领域中锻造您的传奇
            </p>
            <p className="text-gray-500">
              &copy; 2025 角斗领域 - 守望先锋角斗领域构筑工具. 为荣耀而战！
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
