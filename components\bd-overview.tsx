"use client"

import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Download } from "lucide-react"
import ItemImage from "./item-image"
import PowerImage from "./power-image"
import type { HeroPower, Item } from "../utils/data-loader"

interface BDOverviewProps {
  currentHero: string
  selectedPowerSlots: (string | null)[]
  roundItemSelections: Array<Array<{ itemId: string; priority: string } | null>>
  heroPowers: Record<string, HeroPower[]>
  itemsData: Item[]
  onExport?: () => void
}

export default function BDOverview({
  currentHero,
  selectedPowerSlots,
  roundItemSelections,
  heroPowers,
  itemsData,
  onExport,
}: BDOverviewProps) {
  // Helper functions
  const getItemCategoryText = (category: string) => {
    switch (category) {
      case "weapon":
        return "武器"
      case "ability":
        return "技能"
      case "survival":
        return "生存"
      default:
        return "未知"
    }
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case "common":
        return "text-white"
      case "rare":
        return "text-blue-400"
      case "epic":
        return "text-purple-400"
      default:
        return "text-white"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "must":
        return "border-red-500"
      case "affordable":
        return "border-yellow-500"
      case "optional":
        return "border-green-500"
      default:
        return ""
    }
  }

  // Check if any items are selected for overview
  const hasAnyItems = roundItemSelections.some((round) => round.some((slot) => slot !== null))
  const hasAnyPowers = selectedPowerSlots.some((slot) => slot !== null)

  return (
    <Card className="bg-card text-card-foreground bg-gradient-to-b from-amber-50 to-orange-50 border-2 border-orange-500/50 rounded-lg shadow-xl p-6">
      <div className="relative mb-2">
        <h2 className="text-lg md:text-xl font-bold text-center text-orange-600">BD概览</h2>
        {onExport && (
          <Button
            onClick={onExport}
            className="absolute top-0 right-0 bg-green-600 hover:bg-green-700 text-white px-4 py-2"
          >
            <Download className="mr-2 h-4 w-4" />
            导出配置
          </Button>
        )}
      </div>
      <h3 className="text-base md:text-lg font-semibold mb-2 text-center text-gray-700">
        英雄：{currentHero === "soldier76" ? "士兵：76" : "朱诺"}
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {/* Powers Overview */}
        <div>
          <h3 className="text-base font-semibold mb-2 text-center text-blue-600">异能选择</h3>
          <div className="bg-white rounded-lg p-2 border-2 border-blue-500 max-h-[300px] overflow-y-auto">
            {hasAnyPowers ? (
              <div className="space-y-2">
                {[1, 3, 5, 7].map((round, index) => {
                  const powerId = selectedPowerSlots[index]
                  const power = powerId ? heroPowers[currentHero]?.find((p) => p.id === powerId) : null

                  if (!power) return null

                  return (
                    <div
                      key={`power-overview-${index}`}
                      className="relative flex items-center p-2 rounded-lg bg-blue-50 border border-blue-300"
                    >
                      <div className="flex items-center gap-2 flex-1">
                        <PowerImage powerId={power.id} name={power.name} className="rounded-md" size={32} />
                        <div>
                          <div className="font-semibold text-blue-800 text-sm">{power.name}</div>
                          <div className="text-xs text-blue-600">第 {round} 回合</div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            ) : (
              <p className="text-gray-600 text-center text-sm">暂未选择异能。</p>
            )}
          </div>
        </div>

        {/* Items Overview */}
        <div className="md:col-span-4">
          <h3 className="text-base font-semibold mb-2 text-center text-yellow-600">物品选择 (按回合)</h3>
          <div className="bg-white rounded-lg p-2 border-2 border-orange-500 overflow-y-auto">
            {hasAnyItems ? (
              <div className="grid grid-cols-2 gap-4 relative">
                {/* 左列：奇数回合 (1, 3, 5, 7...) */}
                <div className="space-y-3">
                  {roundItemSelections.map((roundSelection, roundIndex) => {
                    // 只显示奇数回合 (roundIndex 0, 2, 4, 6... 对应第 1, 3, 5, 7... 回合)
                    if (roundIndex % 2 !== 0) return null

                    const itemsInRound = roundSelection.filter((slot) => slot !== null)
                    if (itemsInRound.length === 0) return null

                    return (
                      <div key={`round-overview-left-${roundIndex}`} className="mb-2">
                        <div className="text-orange-600 font-semibold text-sm mb-1">第 {roundIndex + 1} 回合:</div>
                        <div className="grid grid-cols-6 gap-2">
                          {itemsInRound.map((slotData, slotIndex) => {
                            const item = itemsData.find((item) => item.id === slotData?.itemId)
                            if (!item) return null

                            return (
                              <div
                                key={`item-overview-${roundIndex}-${slotIndex}`}
                                className="flex flex-col items-center relative"
                              >
                                <div className="relative mb-1">
                                  <ItemImage
                                    itemId={item.id}
                                    name={item.name}
                                    className={`w-8 h-8 rounded flex flex-col items-center justify-center border-2 ${
                                      getPriorityColor(slotData?.priority || "") || "border-gray-300"
                                    } bg-gray-100`}
                                  />
                                </div>
                                <div className="text-xs font-semibold text-center text-gray-800 truncate w-full">
                                  {item.name}
                                </div>
                                <div className="text-xs text-gray-600 text-center">
                                  {getItemCategoryText(item.category)}
                                </div>

                                <div className="group relative">
                                  <div className="hidden group-hover:block absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 z-30">
                                    <div className="bg-gray-800 p-2 rounded-md shadow-lg max-w-xs border border-gray-700 text-xs">
                                      <div className={`font-bold ${getRarityColor(item.rarity)}`}>{item.name}</div>
                                      <div className="text-yellow-300">价格: {item.cost} C</div>
                                      <div className="mt-1">
                                        {item.description &&
                                          item.description.split("\n").map((line, i) => <div key={i}>{line}</div>)}
                                      </div>
                                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800 border-r border-b border-gray-700"></div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      </div>
                    )
                  })}
                </div>

                {/* 分栏线 */}
                <div className="absolute left-1/2 top-0 bottom-0 w-0.5 bg-orange-200 transform -translate-x-1/2"></div>

                {/* 右列：偶数回合 (2, 4, 6, 8...) */}
                <div className="space-y-3">
                  {roundItemSelections.map((roundSelection, roundIndex) => {
                    // 只显示偶数回合 (roundIndex 1, 3, 5, 7... 对应第 2, 4, 6, 8... 回合)
                    if (roundIndex % 2 === 0) return null

                    const itemsInRound = roundSelection.filter((slot) => slot !== null)
                    if (itemsInRound.length === 0) return null

                    return (
                      <div key={`round-overview-right-${roundIndex}`} className="mb-2">
                        <div className="text-orange-600 font-semibold text-sm mb-1">第 {roundIndex + 1} 回合:</div>
                        <div className="grid grid-cols-6 gap-2">
                          {itemsInRound.map((slotData, slotIndex) => {
                            const item = itemsData.find((item) => item.id === slotData?.itemId)
                            if (!item) return null

                            return (
                              <div
                                key={`item-overview-${roundIndex}-${slotIndex}`}
                                className="flex flex-col items-center relative"
                              >
                                <div className="relative mb-1">
                                  <ItemImage
                                    itemId={item.id}
                                    name={item.name}
                                    className={`w-8 h-8 rounded flex flex-col items-center justify-center border-2 ${
                                      getPriorityColor(slotData?.priority || "") || "border-gray-300"
                                    } bg-gray-100`}
                                  />
                                </div>
                                <div className="text-xs font-semibold text-center text-gray-800 truncate w-full">
                                  {item.name}
                                </div>
                                <div className="text-xs text-gray-600 text-center">
                                  {getItemCategoryText(item.category)}
                                </div>

                                <div className="group relative">
                                  <div className="hidden group-hover:block absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 z-30">
                                    <div className="bg-gray-800 p-2 rounded-md shadow-lg max-w-xs border border-gray-700 text-xs">
                                      <div className={`font-bold ${getRarityColor(item.rarity)}`}>{item.name}</div>
                                      <div className="text-yellow-300">价格: {item.cost} C</div>
                                      <div className="mt-1">
                                        {item.description &&
                                          item.description.split("\n").map((line, i) => <div key={i}>{line}</div>)}
                                      </div>
                                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800 border-r border-b border-gray-700"></div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            ) : (
              <p className="text-gray-600 text-center text-sm">暂未配置物品。</p>
            )}
          </div>
        </div>
      </div>
    </Card>
  )
}
