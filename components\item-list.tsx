"use client"

import type { Item } from "../utils/data-loader"
import ItemCard from "./item-card"

interface ItemListProps {
  title: string
  items: Item[]
  rarity: "common" | "rare" | "epic"
  selectedItems: Array<{ itemId: string; priority: string } | null>
  onItemSelect: (itemId: string) => void
}

export default function ItemList({ title, items, rarity, selectedItems, onItemSelect }: ItemListProps) {
  // Rarity color configurations
  const rarityColors = {
    common: "text-green-400",
    rare: "text-blue-400",
    epic: "text-purple-400",
  }

  // Rarity border colors
  const rarityBorderColors = {
    common: "border-green-500",
    rare: "border-blue-500",
    epic: "border-purple-500",
  }

  return (
    <div className={`bg-white rounded-lg p-4 border-2 ${rarityBorderColors[rarity]}`}>
      <h4 className={`text-center font-semibold ${rarityColors[rarity]} mb-3 pb-2 border-b ${rarityBorderColors[rarity]}`}>
        {title} ({items.length})
      </h4>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
        {items.map((item) => {
          const isSelected = selectedItems.some((slot) => slot && slot.itemId === item.id)

          return (
            <ItemCard
              key={item.id}
              item={item}
              isSelected={isSelected}
              onClick={() => !isSelected && onItemSelect(item.id)}
            />
          )
        })}
      </div>
    </div>
  )
}
