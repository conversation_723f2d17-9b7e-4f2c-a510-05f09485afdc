"use client"

import type { Item } from "../utils/data-loader"
import ItemImage from "./item-image"

interface ItemCardProps {
  item: Item
  isSelected: boolean
  onClick: () => void
  showHeroTag?: boolean
  size?: "small" | "medium" | "large"
  priorityBorder?: string
}

export default function ItemCard({
  item,
  isSelected,
  onClick,
  showHeroTag = true,
  size = "small",
  priorityBorder = "",
}: ItemCardProps) {
  // Size configurations
  const sizeClasses = {
    small: "w-8 h-8",
    medium: "w-10 h-10",
    large: "w-12 h-12",
  }

  // Rarity color configurations
  const rarityColors = {
    common: "text-green-400",
    rare: "text-blue-400",
    epic: "text-purple-400",
  }

  // Rarity border colors
  const rarityBorderColors = {
    common: "border-green-500",
    rare: "border-blue-500",
    epic: "border-purple-500",
  }

  return (
    <div
      onClick={onClick}
      className={`relative p-2 rounded border-2 ${
        isSelected
          ? "bg-gray-100 border-gray-400 opacity-50 cursor-not-allowed"
          : `bg-white ${rarityBorderColors[item.rarity]} hover:shadow-md cursor-pointer`
      } ${priorityBorder}`}
    >
      <div className="flex flex-col items-center">
        {showHeroTag && item.hero !== "general" && (
          <div
            className={`absolute top-0 right-0 text-xs font-bold px-1 rounded-bl text-white ${
              item.rarity === "rare" ? "bg-blue-600" : "bg-purple-600"
            }`}
          >
            Hero
          </div>
        )}
        <ItemImage
          itemId={item.id}
          name={item.name}
          className={`${sizeClasses[size]} rounded mb-1`}
          fallbackText={item.name.charAt(0)}
        />
        <div className={`text-xs font-semibold text-center ${rarityColors[item.rarity]}`}>{item.name}</div>
        <div className="text-xs text-orange-600 font-medium">{item.cost} C</div>
      </div>

      <div className="absolute inset-0 opacity-0 hover:opacity-100 bg-gray-900/80 rounded transition-opacity flex items-center justify-center z-20">
        <div className="bg-gray-800 p-2 rounded-md max-w-xs">
          <div
            className={`font-bold text-sm ${
              item.rarity === "common" ? "text-green-400" : item.rarity === "rare" ? "text-blue-400" : "text-purple-400"
            }`}
          >
            {item.name}
          </div>
          <div className="text-xs text-yellow-300">价格: {item.cost} C</div>
          <div className="text-xs mt-1">
            {item.description && item.description.split("\n").map((line, i) => <div key={i}>{line}</div>)}
          </div>
        </div>
      </div>
    </div>
  )
}
